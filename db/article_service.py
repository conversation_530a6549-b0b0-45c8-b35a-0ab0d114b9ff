from datetime import date, datetime
import json
from typing import Any, Dict, List, Optional, Tuple, Union

from sqlalchemy import (
    Date, Integer, cast, func, not_, outerjoin, select, update, text, and_
)
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import selectinload
from sqlalchemy.dialects.postgresql import insert as pg_insert
from uuid import uuid4

import torch

from db.database_connection import DatabaseConnection
from db.utils import with_retries
from db.models import Article, Chunk, LlmApiResults

from utils.huggingface_model_loader import ModelLoader
from utils.logging_config import configure_logging
from utils.timezone_utils import convert_timezone

# Configure logging
logger = configure_logging(__name__, log_file='database.log')

FIELD_MAP = {
    "source": Article.source,
    "sentiment": Article.article_metadata["sentiment_analysis"]["label"].astext,
    "indicator": Article.article_metadata["indicator_analysis"]["label"].astext,
    "llm": Article.article_metadata['llm'],
}

MODEL_NAME = "ProsusAI/finbert"


class EmbeddingService:
    """Handles text encoding and embedding operations."""

    def __init__(self, model_name: str = MODEL_NAME):
        self.model_name = model_name
        self._model = None
        self._tokenizer = None

    @property
    def model(self):
        """Lazy load model."""
        if self._model is None:
            self._load_model()
        return self._model

    @property
    def tokenizer(self):
        """Lazy load tokenizer."""
        if self._tokenizer is None:
            self._load_model()
        return self._tokenizer

    def _load_model(self):
        """Load the embedding model and tokenizer."""
        logger.info("Loading embedding model: %s", self.model_name)
        self._model, self._tokenizer = ModelLoader().load(self.model_name)

    def encode_text(self, text: str) -> List[float]:
        """
        Encode text using the loaded model.

        Args:
            text: Text to encode

        Returns:
            List of floats representing the embedding vector
        """
        if not text or not text.strip():
            raise ValueError("Text cannot be empty")

        try:
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512  # Explicit max length
            )

            with torch.no_grad():
                outputs = self.model(**inputs)
                hidden_states = outputs.last_hidden_state
                attention_mask = inputs['attention_mask'].unsqueeze(-1)
                masked_hidden = hidden_states * attention_mask
                embedding = masked_hidden.sum(1) / attention_mask.sum(1)
                embedding = embedding.squeeze().tolist()

            return embedding

        except Exception as e:
            logger.error("Failed to encode text: %s", str(e))
            raise


class ArticleService:
    def __init__(self, connection: DatabaseConnection):
        self.connection = connection
        self.embedding_service = EmbeddingService()

    @with_retries()
    def run_sql_query(
        self,
        query: str,
        params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Execute a raw SQL query and return results.

        Args:
            query: SQL query string
            params: Optional dictionary of parameters for parameterized queries

        Returns:
            List of dictionaries representing query results

        Raises:
            SQLAlchemyError: For database-related errors
            ValueError: For invalid input parameters
        """
        if not query or not query.strip():
            raise ValueError("Query cannot be empty")

        logger.debug("Executing query: %s...", query[:100])

        with self.connection.get_session() as session:
            try:
                stmt = text(query)

                if params:
                    result = session.execute(stmt, params)
                else:
                    result = session.execute(stmt)

                if result.returns_rows:
                    rows = result.fetchall()
                    return [dict(row._mapping) for row in rows]
                else:
                    return [{"rows_affected": result.rowcount}]

            except SQLAlchemyError as e:
                logger.error("Database error executing query: %s", str(e))
                raise
            except Exception as e:
                logger.error("Unexpected error executing query: %s", str(e))
                raise

    @with_retries()
    def upsert_article_with_chunks(
        self,
        article_data: Dict[str, Any],
        chunks_data: List[Dict[str, Any]]
    ) -> str:
        """
        Insert or update an article with its associated chunks.

        Args:
            article_data: Dictionary containing article data
            chunks_data: List of dictionaries containing chunk data

        Returns:
            Article ID
        """
        if not article_data.get("url"):
            raise ValueError("Article URL is required")

        logger.info("Upserting article with %d chunks", len(chunks_data))

        article_id = article_data.get("id") or str(uuid4())
        article_data["id"] = article_id

        with self.connection.get_session() as session:
            try:
                # Upsert article
                stmt = pg_insert(Article).values(article_data)
                update_cols = {
                    key: getattr(stmt.excluded, key)
                    for key in article_data if key != "id" and article_data[key] is not None
                }
                stmt = stmt.on_conflict_do_update(
                    index_elements=["url"],
                    set_=update_cols
                ).returning(Article.id)

                result = session.execute(stmt).first()
                session.flush()

                if result:
                    article_id = result[0]

                # Delete existing chunks
                session.query(Chunk).filter(
                    Chunk.article_id == article_id
                ).delete()

                # Insert new chunks
                chunks = []
                for chunk_data in chunks_data:
                    if 'embedding' not in chunk_data:
                        chunk_data['embedding'] = self.embedding_service.encode_text(
                            chunk_data['text']
                        )
                        chunk_data['model_name'] = self.embedding_service.model_name

                    chunks.append(Chunk(
                        id=str(uuid4()),
                        article_id=article_id,
                        **chunk_data
                    ))

                session.add_all(chunks)

                logger.info("Article upserted with ID: %s", article_id)
                return article_id

            except Exception as e:
                logger.error("Failed to upsert article: %s", str(e))
                raise

    @with_retries()
    def update_chunk(self, chunk_id: str, update_fields: Dict[str, Any]) -> None:
        """
        Update a specific chunk.

        Args:
            chunk_id: ID of the chunk to update
            update_fields: Dictionary of fields to update
        """
        logger.info("Updating chunk ID: %s", chunk_id)

        with self.connection.get_session() as session:
            stmt = update(Chunk).where(
                Chunk.id == chunk_id).values(**update_fields)
            result = session.execute(stmt)

            if result.rowcount == 0:
                raise ValueError(f"Chunk with ID {chunk_id} not found")

            logger.info("Chunk updated successfully")

    @with_retries()
    def query_articles_by_metadata(
        self,
        metadata_filter: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Query articles by metadata filter.

        Args:
            metadata_filter: Dictionary representing metadata filter

        Returns:
            List of matching articles
        """
        logger.info("Querying articles by metadata: %s", metadata_filter)

        with self.connection.get_session() as session:
            stmt = select(Article).where(
                Article.article_metadata.op("@>")(metadata_filter)
            )
            results = session.execute(stmt).scalars().all()

            logger.info("Found %d articles", len(results))
            return [r.to_dict() for r in results]

    @with_retries()
    def query_similar_chunks(
        self,
        text: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        top_k: int = 5,
        threshold: Optional[float] = None
    ) -> List[Tuple[Dict[str, Any], float]]:
        """
        Query chunks similar to the given text using vector similarity.

        Args:
            text: Query text
            start_date: Optional start date filter
            end_date: Optional end date filter
            top_k: Number of results to return
            threshold: Minimum similarity threshold

        Returns:
            List of tuples containing (chunk, similarity_score)
        """
        logger.info("Querying similar chunks with top_k=%d", top_k)

        query_embedding = self.embedding_service.encode_text(text)

        with self.connection.get_session() as session:
            query = session.query(
                Chunk,
                func.exp(-Chunk.embedding.cosine_distance(query_embedding)
                         ).label('similarity')
            )

            # Apply filters
            filters = []
            if start_date:
                filters.append(Chunk.article_date >= start_date)
            if end_date:
                filters.append(Chunk.article_date <= end_date)
            if threshold:
                filters.append(
                    func.exp(-Chunk.embedding.cosine_distance(query_embedding)) > threshold
                )

            if filters:
                query = query.filter(and_(*filters))

            results = query.order_by(
                Chunk.embedding.cosine_distance(query_embedding)
            ).limit(top_k).all()

            logger.info("Found %d similary chunks", len(results))
            return [(chunk.to_dict(), similarity) for chunk, similarity in results]

    @with_retries()
    def article_exists(self, url: str) -> bool:
        """
        Check if an article exists by URL.

        Args:
            url: Article URL

        Returns:
            True if article exists, False otherwise
        """
        with self.connection.get_session() as session:
            result = session.execute(
                select(Article.id).where(Article.url == url)
            ).first()
            return result is not None

    @with_retries()
    def get_article_by_url(self, url: str) -> Dict[str, Any] | None:
        with self.connection.get_session() as session:
            result = session.execute(
                select(Article).where(Article.url == url)
            ).scalars().first()
            return result.to_dict() if result else None

    @with_retries()
    def get_articles(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        source: Optional[str] = None,
        sentiment: Optional[str] = None,
        indicator: Optional[str] = None,
        search_keyword: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        load_chunks: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Get articles with various filtering options.

        Args:
            start_date: Start date filter
            end_date: End date filter
            source: Source filter
            sentiment: Sentiment filter
            indicator: Indicator filter
            search_keyword: Keyword search in title
            limit: Maximum number of results
            offset: Result offset for pagination
            load_chunks: Whether to load associated chunks

        Returns:
            List of articles matching the criteria
        """
        logger.info("Querying articles with filters")

        with self.connection.get_session() as session:
            stmt = select(Article)

            if load_chunks:
                stmt = stmt.options(selectinload(Article.chunks))

            conditions = []

            # Date filters
            if start_date:
                try:
                    conditions.append(Article.date >= start_date)
                except ValueError:
                    logger.warning("Invalid start_date format: %s", start_date)

            if end_date:
                try:
                    conditions.append(Article.date <= end_date)
                except ValueError:
                    logger.warning("Invalid end_date format: %s", end_date)

            # Other filters
            if source:
                conditions.append(Article.source == source)

            if search_keyword:
                keyword = f"%{search_keyword.lower()}%"
                conditions.append(Article.title.ilike(keyword))

            if sentiment:
                conditions.append(
                    text(
                        'jsonb_path_exists(article_metadata, \'$.sentiment_analysis.label ? (@ == "{}")\')'
                        .format(sentiment)
                    )
                )

            if indicator:
                conditions.append(
                    text(
                        'jsonb_path_exists(article_metadata, \'$.indicator_analysis.label ? (@ == "{}")\')'
                        .format(indicator)
                    )
                )

            # Apply conditions
            if conditions:
                stmt = stmt.where(and_(*conditions))

            # Ordering and pagination
            stmt = stmt.order_by(Article.date.desc())

            if offset:
                stmt = stmt.offset(offset)
            if limit:
                stmt = stmt.limit(limit)

            results = session.execute(stmt).scalars().all()
            logger.info("Found %d articles matching filters", len(results))
            return [r.to_dict(include_chunks=load_chunks) for r in results]

    @with_retries()
    def get_articles_by_influence(self, start_date: Optional[datetime] = None,
                                  end_date: Optional[datetime] = None,
                                  influence_field: str = "influence_tagging.influence",
                                  limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get articles sorted by influence score.

        Args:
            start_date: Start date filter
            end_date: End date filter
            limit: Maximum number of results

        Returns:
            List of articles sorted by influence score
        """
        logger.info("Querying articles with filters")

        with self.connection.get_session() as session:
            stmt = select(Article)
            conditions = []
            if start_date:
                try:
                    conditions.append(Article.date >= start_date)
                except ValueError:
                    logger.warning("Invalid start_date format: %s", start_date)

            if end_date:
                try:
                    conditions.append(Article.date <= end_date)
                except ValueError:
                    logger.warning("Invalid end_date format: %s", end_date)

            field_path = influence_field.split('.')
            field_expr = Article.article_metadata
            for field in field_path:
                conditions.append(field_expr.has_key(field))
                field_expr = field_expr[field]

            if conditions:
                stmt = stmt.where(and_(*conditions))

            stmt = stmt.order_by(
                func.abs(cast(field_expr, Integer)).desc(),
                Article.date.desc()
            )

            if limit:
                stmt = stmt.limit(limit)

            results = session.execute(stmt).scalars().all()
            logger.info("Found %d articles matching filters", len(results))
            return [r.to_dict() for r in results]

    @with_retries()
    def get_article_count(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        additional_filters: Optional[Dict[str, Any]] = None,
        group_by: Optional[List[str]] = None,
    ) -> Union[List[Dict[str, Any]], Dict[str, int]]:
        """
        Get article count statistics with optional grouping and encapsulated field access.

        Args:
            start_date: Optional start date filter
            end_date: Optional end date filter
            additional_filters: Optional filters in form of {field_name: value}
            group_by: Optional list of field names to group by

        Returns:
            List of grouped count dicts or total count dict
        """

        with self.connection.get_session() as session:
            filters = []
            if start_date:
                filters.append(Article.date >= start_date)
            if end_date:
                filters.append(Article.date <= end_date)
            if additional_filters:
                for key, value in additional_filters.items():
                    if key not in FIELD_MAP:
                        raise ValueError(f"Invalid filter key: {key}")
                    filters.append(FIELD_MAP[key] == value)

            if group_by:
                expressions = []
                for key in group_by:
                    if key not in FIELD_MAP:
                        raise ValueError(f"Invalid group_by key: {key}")
                    expressions.append(FIELD_MAP[key].label(key))

                stmt = select(*expressions, func.count().label("count")).where(
                    *filters
                ).group_by(*expressions).order_by(func.count().desc())

                rows = session.execute(stmt).mappings().all()
                return [dict(row) for row in rows]
            else:
                stmt = select(func.count().label("count")).where(*filters)
                count = session.execute(stmt).scalar_one()
                return {"count": count}

    @with_retries()
    def get_article_time_series_count(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        additional_filters: Optional[Dict[str, Any]] = None,
        group_by: Optional[List[str]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Return daily trend of article counts with optional grouping.

        Args:
            start_date: Optional start date filter
            end_date: Optional end date filter
            additional_filters: Optional filters in form of {field_name: value}
            group_by: Optional list of field names to group by in addition to date

        Returns:
            List of daily grouped count dictionaries, sorted by date
        """

        with self.connection.get_session() as session:
            filters = []
            if start_date:
                filters.append(Article.date >= start_date)
            if end_date:
                filters.append(Article.date <= end_date)
            if additional_filters:
                for key, value in additional_filters.items():
                    if key not in FIELD_MAP:
                        raise ValueError(f"Invalid filter key: {key}")
                    filters.append(FIELD_MAP[key] == value)

            # Always group by date
            group_exprs = [cast(FIELD_MAP["date"], Date).label("date")]

            if group_by:
                for key in group_by:
                    if key not in FIELD_MAP:
                        raise ValueError(f"Invalid group_by key: {key}")
                    group_exprs.append(FIELD_MAP[key].label(key))

            stmt = select(
                *group_exprs,
                func.count().label("count")
            ).where(*filters).group_by(*group_exprs).order_by(group_exprs[0].asc())

            rows = session.execute(stmt).mappings().all()
            return [dict(row) for row in rows]

    @with_retries()
    def get_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        source: Optional[str] = None,
        sentiment: Optional[str] = None,
        indicator: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get comprehensive statistics about articles.

        Args:
            start_date: Start date filter
            end_date: End date filter
            source: Source filter
            sentiment: Sentiment filter
            indicator: Indicator filter

        Returns:
            Dictionary containing various statistics
        """
        with self.connection.get_session() as session:
            # Build base filters
            filters = []
            if start_date:
                filters.append(Article.date >= start_date)
            if end_date:
                filters.append(Article.date <= end_date)
            if source:
                filters.append(Article.source == source)
            if sentiment:
                filters.append(
                    Article.article_metadata["sentiment_analysis"]["label"].astext == sentiment
                )
            if indicator:
                filters.append(
                    Article.article_metadata["indicator_analysis"]["label"].astext == indicator
                )

            # Basic statistics
            stats_stmt = select(
                func.count().label("total_articles"),
                func.count(func.distinct(Article.source)
                           ).label("unique_sources"),
            ).where(*filters)
            stats = session.execute(stats_stmt).mappings().one()

            # Source breakdown
            source_stmt = select(
                Article.source.label("source"),
                func.count().label("count")
            ).where(*filters).group_by(Article.source).order_by(func.count().desc())
            sources = session.execute(source_stmt).mappings().all()

            # Sentiment breakdown
            sentiment_stmt = select(
                Article.article_metadata["sentiment_analysis"]["label"].astext.label(
                    "sentiment"),
                func.count().label("count")
            ).where(*filters).group_by(
                Article.article_metadata["sentiment_analysis"]["label"].astext
            ).order_by(func.count().desc())
            sentiments = session.execute(sentiment_stmt).mappings().all()

            # Indicator breakdown
            indicator_stmt = select(
                Article.article_metadata["indicator_analysis"]["label"].astext.label(
                    "indicator"),
                func.count().label("count")
            ).where(*filters).group_by(
                Article.article_metadata["indicator_analysis"]["label"].astext
            ).order_by(func.count().desc())
            indicators = session.execute(indicator_stmt).mappings().all()

            return {
                **dict(stats),
                "by_source": [dict(row) for row in sources],
                "by_sentiment": [dict(row) for row in sentiments],
                "by_indicator": [dict(row) for row in indicators],
            }

    @with_retries()
    def get_time_series_trend(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        with self.connection.get_session() as session:
            stmt = select(
                cast(Article.date, Date).label("date"),
                func.count().label("article_count")
            )

            # Apply filters only if values are provided
            if start_date:
                stmt = stmt.where(Article.date >= start_date)
            if end_date:
                stmt = stmt.where(Article.date <= end_date)

            stmt = stmt.group_by(cast(Article.date, Date)).order_by("date")

            results = session.execute(stmt).mappings().all()
            return [dict(r) for r in results]

    @with_retries()
    def get_article_date_range(self) -> Tuple[datetime, datetime]:
        """
        Get the earliest and latest article_date in the articles table.

        Returns:
            Tuple of (min_date, max_date) as strings in YYYY-MM-DD format,
        """
        with self.connection.get_session() as session:
            stmt = select(func.min(Article.date),
                          func.max(Article.date))
            result = session.execute(stmt).one_or_none()
            return result

    @with_retries()
    def get_articles_for_llm_batch(
        self,
        prompt_type: str,
        excluded_statuses: List[str],
        date_list: Optional[List[date]] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: Optional[int] = None,
        min_words: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        Fetch articles for LLM processing, excluding those already processed
        by the given API with the given prompt type and any of the given statuses.

        Args:
            llm_prompt_type: Prompt type to exclude if already processed or present in metadata
            llm_success_status: Exclude success llm results
            date_list: List of specific dates
            start_date: Start date (inclusive)
            end_date: End date (inclusive)
            limit: Maximum number of articles to return
            min_words: Minimum combined word count of title + content

        Returns:
            List of article dicts
        """
        with self.connection.get_session() as session:
            stmt = select(Article)

            filters = []

            # Date filters
            if date_list:
                filters.append(cast(Article.date, Date).in_(date_list))
            else:
                if start_date:
                    filters.append(Article.date >= start_date)
                if end_date:
                    filters.append(Article.date <= end_date)

            # Exclude articles that already have the prompt_type in metadata
            filters.append(
                not_(Article.article_metadata.has_key(prompt_type)))

            # Exclude articles that already have matching LLM results with given status(es)
            subquery = select(1).where(
                and_(
                    LlmApiResults.article_id == Article.id,
                    LlmApiResults.prompt_type == prompt_type,
                    LlmApiResults.status.in_(excluded_statuses)
                )
            ).exists()

            filters.append(not_(subquery))

            # Word count filter: count words in title + content
            if min_words is not None:
                word_count_expr = (
                    func.coalesce(func.array_length(func.string_to_array(Article.title, ' '), 1), 0) +
                    func.coalesce(func.array_length(
                        func.string_to_array(Article.content, ' '), 1), 0)
                )
                filters.append(word_count_expr >= min_words)

                stmt = stmt.where(and_(*filters))

            if limit:
                stmt = stmt.limit(limit)

            results = session.execute(stmt).scalars().all()
            return [r.to_dict() for r in results]

    @with_retries()
    def add_article_metadata(self, article_id: str, updates: dict) -> bool:
        """
        Merge nested updates into the article_metadata JSONB column.

        Args:
            article_id: ID of the article to update.
            updates: Fully nested dict to merge into article_metadata.
        """
        stmt = text("""
            UPDATE articles
            SET article_metadata = article_metadata || CAST(:value AS jsonb)
            WHERE id = :article_id
        """)

        with self.connection.get_session() as session:
            result = session.execute(stmt, {
                "value": json.dumps(updates),
                "article_id": article_id
            })
            if result:
                return True
            return False
