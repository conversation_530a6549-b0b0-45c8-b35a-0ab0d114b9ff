import re
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List
from dotenv import load_dotenv

# Import logging configuration
from apis.llm.data_types import (
    ACTIVE_BATCH_STATUSES,
    COMPLETED_BATCH_STATUSES,
    BatchResponse,
    CompletionRequest,
    CompletionResponse,
    CompletionStatus
)
from nlp.utils import estimate_tokens
from utils.logging_config import configure_logging

# Configure logger for this module
logger = configure_logging(__name__, log_file='llm_base.log')

load_dotenv()


class RateLimitExeption(Exception):
    def __init__(self, limit, message="API rate limit exceeded"):
        self.limit = limit
        self.message = message
        super().__init__(f"{message} (Limit: {limit})")


class BudgetLimitExeption(Exception):
    def __init__(self, limit, message="API budget limit exceeded"):
        self.limit = limit
        self.message = message
        super().__init__(f"{message} (Limit: {limit})")


class MaxTokensException(Exception):
    def __init__(self, limit, message="Max enqueued tokens exceeded"):
        self.limit = limit
        self.message = message
        super().__init__(f"{message} (Limit: {limit})")


class BaseAPIManager(ABC):
    """
    Abstract base class for API managers with common functionality.
    Features:
    - Rate limiting (requests per minute)
    - Budget tracking
    - Request logging
    - Error handling and retries
    """

    def __init__(
        self,
        total_budget: float = 0.0,
        total_cost: float = 0.0,
        requests_per_minute: int = 10,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        max_enqueued_tokens: Optional[int] = None,
        enqueued_tokens: int = 0,
        model_pricing: Dict[str, Dict[str, float]] = None,
        batch_discount: float = 0.5,
    ):
        """
        Initialize the API manager.

        Args:
            total_budget: Total budget for API calls
            total_cost: Total cost so far
            requests_per_minute: Maximum requests per minute
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
            max_enqueued_tokens: Maximum number of tokens that can be enqueued
            enqueued_tokens: Number of tokens already enqueued
            model_pricing: Pricing information for models
            batch_discount: Discount applied to batch requests
        """
        self.total_budget = total_budget
        self.total_cost = total_cost
        self.requests_per_minute = requests_per_minute
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.max_enqueued_tokens = max_enqueued_tokens
        self.enqueued_tokens = enqueued_tokens
        self.model_pricing = model_pricing or {}
        self.batch_discount = batch_discount

        # Rate limiting
        self.request_times: List[float] = []
        self.total_estimated_cost = 0.0

    @abstractmethod
    def _initialize_client(self):
        """Initialize the API client."""
        pass

    @abstractmethod
    def _make_completion_request(self, request: CompletionRequest) -> CompletionResponse:
        """Make the actual API request for completion."""
        pass

    @abstractmethod
    def _create_batch_request(self, requests: List[CompletionRequest]) -> BatchResponse:
        """Create a batch request."""
        pass

    @abstractmethod
    def _retrieve_batch_status_request(self, batch_id: str) -> BatchResponse:
        """Retrieve batch status."""
        pass

    @abstractmethod
    def _retrieve_batch_results_request(self, batch_id: str) -> List[CompletionResponse]:
        """Retrieve batch results."""
        pass

    @abstractmethod
    def _list_batches_request(self, limit: int) -> List[BatchResponse]:
        """List all Message Batches."""
        pass

    def _check_rate_limit(self):
        """Check and enforce rate limiting."""
        current_time = time.time()
        # Remove requests older than 1 minute
        self.request_times = [
            t for t in self.request_times if current_time - t < 60]

        if len(self.request_times) >= self.requests_per_minute:
            raise RateLimitExeption(self.requests_per_minute)

    def _check_budget_limit(self, estimated_cost: float):
        """Check if the request would exceed the budget limit."""

        if self.total_cost + estimated_cost > self.total_budget:
            logger.critical(
                f"{self.__class__.__name__}: "
                f"Request would exceed budget limit. Current: ${self.total_cost:.4f}, "
                f"Estimated cost: ${estimated_cost:.4f}, Limit: ${self.total_budget:.4f}"
            )
            raise BudgetLimitExeption(self.total_budget)

    def _estimate_cost(self, model: str, prompt_tokens: int, completion_tokens: int, is_batch: bool = False) -> float:
        """Estimate the cost of a request based on token counts."""
        base_model = max(
            (k for k in self.model_pricing if model.startswith(k)), key=len, default=None)

        if not base_model:
            logger.warning(f"Pricing not available for model {model}")
            return 10000.0

        input_rate = self.model_pricing[base_model]['input_rate']
        output_rate = self.model_pricing[base_model]['output_rate']

        input_cost = (prompt_tokens / 1000) * input_rate
        output_cost = (completion_tokens / 1000) * output_rate

        total_cost = input_cost + output_cost

        total_cost = total_cost * self.batch_discount if is_batch else total_cost
        logger.debug(
            f"{self.__class__.__name__}: "
            f"Estimated cost: ${total_cost:.4f}, "
            f"Prompt tokens: {prompt_tokens}, "
            f"Completion tokens: {completion_tokens}"
        )
        return total_cost

    def get_completion(self, request: CompletionRequest) -> CompletionResponse | None:
        """
        Get a completion from the API with rate limiting and budget control.
        """
        # Check rate limit
        self._check_rate_limit()

        # Estimate cost (rough estimate based on prompt length)
        prompt_text = (request.user_prompt or "") + \
            (request.system_prompt or "")
        estimated_tokens = estimate_tokens(prompt_text)
        estimated_cost = self._estimate_cost(
            request.model, estimated_tokens, request.max_tokens)

        # Check budget limit
        self._check_budget_limit(estimated_cost)

        # Make the request with retries
        for attempt in range(self.max_retries):
            try:
                completion = self._make_completion_request(request)

                # Update request tracking
                self.request_times.append(time.time())

                # Calculate actual cost
                actual_cost = self._estimate_cost(
                    request.model,
                    completion.input_tokens,
                    completion.output_tokens
                )
                self.total_cost += actual_cost
                completion.cost = actual_cost

                # Log the request
                logger.info(
                    f"{self.__class__.__name__}: "
                    f"Request completed. Cost: ${actual_cost:.4f}, "
                    f"Total cost: ${self.total_cost:.4f}"
                )

                return completion

            except Exception as e:
                if attempt == self.max_retries - 1:
                    logger.error(
                        f"{self.__class__.__name__}: "
                        f"Failed after {self.max_retries} attempts: {str(e)}")
                    return None
                logger.warning(
                    f"{self.__class__.__name__}: "
                    f"Attempt {attempt + 1} failed: {str(e)}"
                )
                time.sleep(self.retry_delay * (attempt + 1))

    def get_completion_batch(self, requests: List[CompletionRequest]) -> BatchResponse | None:
        # Check rate limit
        self._check_rate_limit()

        batch_estimate_tokens = 0
        batch_estimated_cost = 0.0
        for request in requests:
            # Estimate cost (rough estimate based on prompt length)
            estimated_tokens = estimate_tokens(
                (request.user_prompt or "") + (request.system_prompt or ""))
            estimated_cost = self._estimate_cost(
                request.model, estimated_tokens, request.max_tokens, is_batch=True)
            request.estimated_tokens = estimated_tokens
            request.estimated_cost = estimated_cost

            batch_estimate_tokens += estimated_tokens
            batch_estimated_cost += estimated_cost
        self.total_estimated_cost += batch_estimated_cost

        if self.max_enqueued_tokens and self.enqueued_tokens + batch_estimate_tokens > self.max_enqueued_tokens:
            logger.error(
                f"{self.__class__.__name__}: "
                f"Batch would exceed max enqueued tokens. Batch: {batch_estimate_tokens}, "
                f"Limit: {self.max_enqueued_tokens}, "
                f"Enqueued: {self.enqueued_tokens}"
            )
            raise MaxTokensException(self.max_enqueued_tokens)

        self.enqueued_tokens += batch_estimate_tokens

        logger.info(
            f"{self.__class__.__name__}: "
            f"Batch estimated cost: ${batch_estimated_cost:.4f}, "
            f"Total estimated cost: ${self.total_estimated_cost:.4f}, "
            f"Current cost: ${self.total_cost:.4f}, "
            f"Total budget: ${self.total_budget:.4f}"
        )

        # Check budget limit
        self._check_budget_limit(self.total_estimated_cost)

        try:
            batch_response = self._create_batch_request(requests)

            # Update request tracking
            self.request_times.append(time.time())

            # Add estimated cost and tokens
            batch_response.estimated_tokens = batch_estimate_tokens
            batch_response.estimated_cost = batch_estimated_cost

            logger.info(
                f"{self.__class__.__name__}: Batch request created {batch_response.id}")
            return batch_response
        except Exception as e:
            logger.error(
                f"{self.__class__.__name__}: Batch request failed. Error {e}")
            return None

    def retrieve_batch(self, batch_id: str) -> BatchResponse | None:
        """Retrieve the status of a batch request."""
        try:
            batch_response = self._retrieve_batch_status_request(batch_id)

            if batch_response.status in COMPLETED_BATCH_STATUSES:
                batch_results = self._retrieve_batch_results(batch_id)
                batch_response.cost = sum([r.cost for r in batch_results])
                batch_response.input_tokens = sum(
                    [r.input_tokens for r in batch_results])
                batch_response.output_tokens = sum(
                    [r.output_tokens for r in batch_results])
                batch_response.completion_results = batch_results

                self.total_cost += batch_response.cost
                self.enqueued_tokens -= batch_response.input_tokens

                # Log the request
                logger.info(
                    f"{self.__class__.__name__}: "
                    f"Batch request completed. Cost: ${batch_response.cost:.4f}, "
                    f"Input tokens: {batch_response.input_tokens}, "
                    f"Output tokens: {batch_response.output_tokens}, "
                    f"Total cost: ${self.total_cost:.4f}, "
                    f"Total enqueued tokens: {self.enqueued_tokens}"
                )

            return batch_response
        except Exception as e:
            logger.error(
                f"{self.__class__.__name__}: "
                f"Failed to retrieve status for batch {batch_id}. Error {e}")
            return None

    def _retrieve_batch_results(self, batch_id: str) -> List[CompletionResponse]:
        """Retrieve the results of a completed batch request."""
        try:
            batch_results = self._retrieve_batch_results_request(batch_id)

            for result in batch_results:
                # Calculate actual cost
                if result.status == CompletionStatus.SUCCEEDED.value:
                    actual_cost = self._estimate_cost(
                        result.model, result.input_tokens, result.output_tokens, is_batch=True)
                    result.cost = actual_cost
                else:
                    logger.error(f"{self.__class__.__name__}: "
                                 f"Failed completion result {result}")

            return batch_results
        except Exception as e:
            logger.error(
                f"{self.__class__.__name__}: "
                f"Failed to retrieve results for batch {batch_id}. Error {e}")
            return []
