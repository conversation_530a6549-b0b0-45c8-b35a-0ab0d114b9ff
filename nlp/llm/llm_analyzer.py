"""
LLM-based news analyzer.

This module provides a news analyzer that uses Large Language Models (LLMs)
to analyze news articles. It supports both single article analysis and batch processing.
"""

from utils.logging_config import get_nlp_logger
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import argparse
from typing import Optional, Dict, Any
from time import sleep

from db.database import DatabaseManager
from apis.llm.base import BaseAPIManager, BudgetLimitExeption, RateLimitExeption
from apis.llm.data_types import CompletionRequest, CompletionStatus
from apis.llm.anthropic import AnthropicManager
from apis.llm.gemini import GeminiManager
from apis.llm.openai import OpenAIManager

from nlp.llm.prompt import PromptManager
from nlp.llm.helpers import TZ, LLMConfig, extract_article_id, generate_custom_id, process_article


@dataclass
class APIFailureState:
    """Tracks failure state for an API."""
    failure_count: int = 0
    last_failure_time: Optional[datetime] = None
    last_failure_reason: Optional[str] = None
    current_backoff: float = 0.0
    next_retry_time: Optional[datetime] = None


# Configure logging
logger = get_nlp_logger(
    __name__, log_file=f'llm/llm_analyzer-{datetime.now().strftime("%Y-%m-%d-%H-%M-%S")}.log')


@dataclass
class APIConfig:
    """Configuration for a specific API processor."""
    # Budget and rate limiting
    total_budget: float = 0.0
    requests_per_minute: int = 30

    # Backoff configuration
    initial_backoff: float = 5.0  # Initial delay in seconds
    max_backoff: float = 300.0  # Maximum delay in seconds
    backoff_multiplier: float = 2.0  # Exponential backoff multiplier

    # LLM-specific configuration
    llm_config: Optional[LLMConfig] = None


class LLMAnalyzer:
    """
    Main class for analyzing news articles using LLM APIs.

    Provides both single article analysis and batch processing capabilities
    with proper error handling, caching, and structured output storage.
    """

    def __init__(self, api_name: str, propmt_type: str, api_config: APIConfig):
        """Initialize the news analyzer with configuration."""
        self.config = api_config
        self.db = DatabaseManager()
        self.prompt_manager = PromptManager()
        # Initialize LLM API
        self.api_name = api_name
        self.propmt_type = propmt_type
        self.llm_api = self._initialize_llm_api()
        self.failure_state = APIFailureState()

        logger.info("LLMAnalyzer initialized successfully")

    def _initialize_llm_api(self) -> BaseAPIManager:
        """Initialize the appropriate LLM API based on configuration."""
        api_mapping = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager,
            'gemini': GeminiManager
        }

        api_class = api_mapping.get(self.api_name)
        if not api_class:
            raise ValueError(f"Unsupported API: {self.api_name}")

        current_cost = self.db.llm_api_service.get_total_cost(
            api=self.api_name, prompt_type=self.propmt_type)

        llm_api = api_class(
            total_budget=self.config.total_budget,
            total_cost=current_cost,
            requests_per_minute=self.config.requests_per_minute
        )

        logger.info(f"Initialized {self.api_name} API with: "
                    f"budget {self.config.total_budget}, "
                    f"current cost {current_cost}")
        return llm_api

    def handle_api_failure(self, error: Exception) -> None:
        """Manage failure tracking, backoff calculation, and retry queue placement."""
        current_time = datetime.now()

        # Update api failure state
        self.failure_state.failure_count += 1
        self.failure_state.last_failure_time = current_time
        self.failure_state.last_failure_reason = error

        # Calculate backoff delay and set next retry time
        backoff_time = self.config.initial_backoff if self.failure_state.current_backoff == 0 else min(
            self.failure_state.current_backoff * self.config.backoff_multiplier,
            self.config.max_backoff
        )
        self.failure_state.current_backoff = backoff_time
        logger.warning(
            f"API {self.api_name} failed (#{self.failure_state.failure_count}), backing off for {backoff_time:.1f}s. Error {error}")
        sleep(backoff_time)

    def save_completion(self, result_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Save completion result to database."""
        try:
            article_id = extract_article_id(result_dict['custom_id'])
            db_record = {
                "id": result_dict['custom_id'],
                'article_id': article_id,
                "api": self.api_name,
                "prompt_type": self.propmt_type,
                "model": result_dict['model'],
                "status": result_dict['status'],
                "raw_response": result_dict.get('raw_response'),
                "input_tokens": result_dict.get('input_tokens'),
                "output_tokens": result_dict.get('output_tokens')
            }
            # if 'user_prompt' in result_dict and 'system_prompt' in result_dict:
            #     db_record['prompt'] = result_dict['system_prompt'] + \
            #         result_dict['user_prompt']

            if 'cost' in result_dict:
                db_record['cost'] = result_dict['cost']

            if 'content' in result_dict:
                parsed_result = self.prompt_manager.parse_result(
                    result_dict['content'], self.propmt_type)
                db_record['content'] = parsed_result

                # Save to article metadata
                if self.db.article_service.add_article_metadata(
                        article_id, {self.propmt_type: db_record['content']}):
                    logger.info(
                        f"Successfully saved in article metadata: {article_id} ")
                else:
                    logger.error(
                        f"Failed to save in article metadata: {article_id}")

            if self.db.llm_api_service.upsert_llm_result(db_record):
                logger.info(
                    f"Successfully saved result: {result_dict['custom_id']}")
            else:
                logger.error(
                    f"Failed to save result: {result_dict['custom_id']}")

            return db_record
        except Exception as e:
            logger.error(f"Error saving result: {e}")

        return {}

    def analyze_article(self, article: Dict[str, Any], custom_id: Optional[str] = None) -> Dict[str, Any]:
        """Analyze a single article using the specified prompt type."""
        try:
            min_input = self.config.llm_config.min_input
            max_input = self.config.llm_config.max_input
            # Process article input
            article_input = process_article(
                article, min_input, max_input, logger=logger)
            if not article_input:
                logger.error(f"Skipped short article {article['id']}")
                return {}

            llm_config = self.config.llm_config
            # Get and format prompt
            prompt = self.prompt_manager.get_prompt(self.propmt_type)
            system_prompt = prompt['system_prompt']
            formatted_prompt = prompt['prompt_template'].format(
                title=article_input['title'],
                content=article_input['content']
            )

            request = CompletionRequest(
                max_tokens=llm_config.max_tokens,
                temperature=llm_config.temperature,
                user_prompt=formatted_prompt,
                system_prompt=system_prompt,
                model=llm_config.model
            )

            # Call LLM API
            completion = self.llm_api.get_completion(request)

            if completion and completion.status == CompletionStatus.SUCCEEDED.value and completion.content:
                logger.info(
                    f"Successfully got completion for article {article['id']}")
                self.failure_state.current_backoff = 0.0

                completion_dict = completion.to_dict()
                completion_dict['custom_id'] = custom_id or generate_custom_id(
                    article['id'], self.propmt_type, self.api_name)
                completion_dict['user_prompt'] = formatted_prompt
                completion_dict['system_prompt'] = system_prompt

                return self.save_completion(completion_dict)
            else:
                logger.error(
                    f"Failed to get completion for article {article['id']}")

        except BudgetLimitExeption as e:
            logger.error(f"Budget limit reached: {e}")
            raise
        except RateLimitExeption as e:
            logger.error(f"Rate limit reached: {e}")
            self.handle_api_failure(e)
        except Exception as e:
            logger.error(
                f"Error analyzing article {article.get('id', 'unknown')}: {e}")
        return {}

    def analyze_article_by_url(self, url: str, override: bool = False) -> Dict[str, Any]:
        """Analyze a specific article by URL."""
        try:
            article = self.db.article_service.get_article_by_url(url)
            if not article:
                logger.warning(f"Article not found: {url}")
                return {}

            cid = generate_custom_id(
                article['id'], self.propmt_type, self.api_name)

            # Check if already analyzed
            if not override:
                existing_result = self.db.llm_api_service.get_result_by_id(cid)
                if existing_result:
                    logger.info(f"Returning existing result for {url}")
                    return {self.propmt_type: existing_result['content']}

            return self.analyze_article(article, cid)

        except Exception as e:
            logger.error(f"Error analyzing article by URL {url}: {e}")

        return {}


def main():
    """Main function to run the analyzer from command line."""
    parser = argparse.ArgumentParser(
        description='Analyze news articles using LLM')

    # Basic options
    parser.add_argument('-u', '--url', help='URL of article to analyze')
    parser.add_argument('-a', '--api', default='gemini', help='LLM API name')
    parser.add_argument(
        '-m', '--model', default='gemini-2.0-flash-001', help='LLM model to use')
    parser.add_argument('-t', '--prompt-type',
                        default='influence_tagging', help='Type of analysis to perform')
    parser.add_argument('-s', '--start-date', help='Start date (YYYY-MM-DD)')
    parser.add_argument('-e', '--end-date', help='End date (YYYY-MM-DD)')

    # Resource limits
    parser.add_argument('-b', '--budget', type=float, default=5.0,
                        help='Maximum budget for API calls')
    parser.add_argument('--max-articles', type=int, default=1000,
                        help='Maximum articles to process per cycle')
    parser.add_argument('--max-input', type=int, default=1024,
                        help='Maximum input words')
    parser.add_argument('--max-tokens', type=int, default=150,
                        help='Maximum output tokens')
    parser.add_argument('--min-input', type=int, default=100,
                        help='Minimum input words')
    parser.add_argument('--requests-per-minute', type=int, default=30,
                        help='Rate limit per minute')
    parser.add_argument('--temperature', type=float, default=0.1,
                        help='Temperature for LLM output')
    parser.add_argument('--override', action='store_true',
                        help='Override existing results')

    args = parser.parse_args()

    try:
        # Initialize analyzer
        api_config = APIConfig(
            total_budget=args.budget,
            requests_per_minute=args.requests_per_minute,
            llm_config=LLMConfig(
                model=args.model,
                max_tokens=args.max_tokens,
                max_input=args.max_input,
                min_input=args.min_input,
                temperature=args.temperature
            )
        )
        analyzer = LLMAnalyzer(args.api, args.prompt_type, api_config)

        if args.url:
            result = analyzer.analyze_article_by_url(args.url, args.override)
            if result:
                print(json.dumps(result, indent=2))
            else:
                print(f"Analysis failed for URL: {args.url}")
        if args.start_date:
            start_date = datetime.strptime(
                args.start_date, "%Y-%m-%d").astimezone(TZ)
            end_date = None
            if args.end_date:
                end_date = datetime.strptime(
                    args.end_date, "%Y-%m-%d").astimezone(TZ)
            if args.override:
                articles = analyzer.db.article_service.get_articles(
                    start_date, end_date, limit=args.max_articles)
            else:
                articles = analyzer.db.article_service.get_articles_for_llm_batch(
                    # Exclude already succeeded articles with the api and prompt type
                    prompt_type=args.prompt_type,
                    excluded_statuses=[
                        CompletionStatus.SUCCEEDED.value,
                        CompletionStatus.IN_PROGRESS.value
                    ],
                    start_date=start_date,
                    end_date=end_date,
                    limit=args.max_articles,
                    min_words=args.min_input
                )
            logger.info(f"Found {len(articles)} articles to analyze")
            for article in articles:
                result = analyzer.analyze_article(article)

    except Exception as e:
        logger.error(f"Application error: {e}")


if __name__ == '__main__':
    main()
