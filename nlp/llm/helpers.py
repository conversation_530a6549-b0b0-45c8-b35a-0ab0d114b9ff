# Constants
from dataclasses import dataclass
from logging import Logger
from typing import Any, Dict
from zoneinfo import ZoneInfo

from nlp.llm.prompt import PROMPT_SHORT_NAME


API_SHORT_NAMES = {
    'openai': 'OAI',
    'anthropic': 'ANC',
    'gemini': 'GEM'
}
MAX_CUSTOM_ID_CHARS = 64
TZ_NAME = "America/New_York"
TZ = ZoneInfo("America/New_York")


@dataclass
class LLMConfig:
    """Configuration for analysis settings."""
    model: str
    max_tokens: int = 150
    max_input: int = 1024
    min_input: int = 200
    temperature: float = 0.1


def extract_article_id(custom_id: str) -> str:
    """Extract article ID from custom ID."""
    return custom_id.split("_")[1]


def generate_custom_id(article_id: str, prompt_type: str, api_name: str) -> str:
    """Generate a custom ID for the analysis request."""
    prompt_sn = PROMPT_SHORT_NAME[prompt_type]
    api_sn = API_SHORT_NAMES[api_name]
    custom_id = f"{api_sn}_{article_id}_{prompt_sn}"

    if len(custom_id) > MAX_CUSTOM_ID_CHARS:
        raise ValueError(
            f"Custom ID too long: {len(custom_id)} > {MAX_CUSTOM_ID_CHARS}")

    return custom_id


def process_article(article: Dict[str, Any], min_input: int, max_input: int, logger: Logger) -> Dict[str, Any]:
    """Process and validate article input."""
    title_words = article.get('title', '').split()
    content_words = article.get('content', '').split()

    total_words = len(title_words) + len(content_words)

    if total_words < min_input:
        logger.debug(
            f"Skipped short article {article.get('url')} of {total_words} words.")
        return None

    if total_words > max_input:
        logger.debug(
            f"Trimmed long article from {total_words} to {max_input} words")

        if len(title_words) > max_input:
            title_words = title_words[:max_input]
            content_words = []
        else:
            content_words = content_words[:max_input - len(title_words)]

    return {
        'title': ' '.join(title_words),
        'content': ' '.join(content_words)
    }
