// Prediction Display Functionality

document.addEventListener('DOMContentLoaded', function () {
    // Wait a bit more to ensure all elements are loaded
    setTimeout(function () {
        initializePredictionInterface();
    }, 100);
});

function initializePredictionInterface() {
    // DOM elements for both the old and new prediction sections
    const predictionContainer = document.getElementById('prediction-container');
    const predictionMain = document.getElementById('prediction-main');
    const predictionConfidence = document.getElementById('prediction-confidence');
    const predictionArticles = document.getElementById('prediction-articles');

    // LLM prediction elements
    const llmPredictionMain = document.getElementById('llm-prediction-main');
    const llmPredictionConfidence = document.getElementById('llm-prediction-confidence');
    const llmKeyEvidence = document.getElementById('llm-key-evidence');
    const llmDominantTheme = document.getElementById('llm-dominant-theme');
    const llmApiSelect = document.getElementById('llm-api-select');
    const llmApiBadge = document.getElementById('llm-api-badge');

    // Prediction section containers
    const traditionalSection = document.getElementById('traditional-prediction-section');
    const llmSection = document.getElementById('llm-prediction-section');

    // Check if we have the new prediction section elements
    const hasNewPredictionSection = predictionMain && predictionConfidence && predictionArticles;
    const hasLLMPredictionSection = llmPredictionMain && llmPredictionConfidence && llmKeyEvidence;

    // Log the elements to help with debugging
    console.log('Prediction elements found:', {
        predictionContainer: !!predictionContainer,
        predictionMain: !!predictionMain,
        predictionConfidence: !!predictionConfidence,
        predictionArticles: !!predictionArticles,
        hasNewPredictionSection: hasNewPredictionSection,
        llmPredictionMain: !!llmPredictionMain,
        llmPredictionConfidence: !!llmPredictionConfidence,
        llmKeyEvidence: !!llmKeyEvidence,
        hasLLMPredictionSection: hasLLMPredictionSection
    });

    // If no prediction elements are found, log warning and exit
    if (!predictionContainer && !hasNewPredictionSection) {
        console.warn('No prediction elements found on this page');
        return;
    }

    // Variables
    let currentPriceValue = null;
    let currentPredictionData = null;
    let currentLLMPredictionData = null;

    // Initialize the prediction section
    console.log('Initializing prediction with current price');
    setupPredictionTypeToggle();
    loadCurrentPrice();

    // Add event listeners for the new prediction section buttons
    const runPredictionBtnMain = document.getElementById('run-prediction-btn-main');
    if (runPredictionBtnMain) {
        runPredictionBtnMain.addEventListener('click', function () {
            showLoading();
            loadPredictionData(currentPriceValue);

            // Also load LLM prediction if LLM section is visible
            const showLLM = document.getElementById('show-llm');
            const showBoth = document.getElementById('show-both');
            if ((showLLM && showLLM.checked) || (showBoth && showBoth.checked)) {
                const selectedApi = llmApiSelect ? llmApiSelect.value : 'gemini';
                loadLLMPredictionData(currentPriceValue, selectedApi);
            }
        });
    } else {
        console.warn('Run prediction button not found');
    }

    const viewHistoryBtnMain = document.getElementById('view-history-btn-main');
    if (viewHistoryBtnMain) {
        viewHistoryBtnMain.addEventListener('click', function () {
            showHistoryModal();
        });
    }

    const viewAllArticlesBtn = document.getElementById('view-all-articles-btn');
    if (viewAllArticlesBtn) {
        viewAllArticlesBtn.addEventListener('click', function () {
            if (currentPredictionData && currentPredictionData.relevant_articles) {
                showArticlesModal(currentPredictionData.relevant_articles);
            }
        });
    }

    // LLM prediction event listeners
    const viewLLMOutlookBtn = document.getElementById('view-llm-outlook-btn');
    if (viewLLMOutlookBtn) {
        viewLLMOutlookBtn.addEventListener('click', function () {
            if (currentLLMPredictionData) {
                showLLMOutlookModal(currentLLMPredictionData);
            }
        });
    }

    const viewCriticalLevelsBtn = document.getElementById('view-critical-levels-btn');
    if (viewCriticalLevelsBtn) {
        viewCriticalLevelsBtn.addEventListener('click', function () {
            if (currentLLMPredictionData) {
                showCriticalLevelsModal(currentLLMPredictionData);
            }
        });
    }

    // LLM API selection change
    if (llmApiSelect) {
        llmApiSelect.addEventListener('change', function () {
            const selectedApi = this.value;
            if (llmApiBadge) {
                llmApiBadge.textContent = selectedApi.charAt(0).toUpperCase() + selectedApi.slice(1);
            }
            // Reload LLM prediction with new API if LLM section is visible
            if (llmSection && !llmSection.style.display.includes('none')) {
                loadLLMPredictionData(currentPriceValue, selectedApi);
            }
        });
    }

    // Setup prediction type toggle functionality
    function setupPredictionTypeToggle() {
        const showTraditional = document.getElementById('show-traditional');
        const showLLM = document.getElementById('show-llm');
        const showBoth = document.getElementById('show-both');

        if (showTraditional) {
            showTraditional.addEventListener('change', function () {
                if (this.checked) {
                    showPredictionSections('traditional');
                }
            });
        }

        if (showLLM) {
            showLLM.addEventListener('change', function () {
                if (this.checked) {
                    showPredictionSections('llm');
                    // Load LLM prediction if not already loaded
                    if (!currentLLMPredictionData) {
                        loadLLMPredictionData(currentPriceValue);
                    }
                }
            });
        }

        if (showBoth) {
            showBoth.addEventListener('change', function () {
                if (this.checked) {
                    showPredictionSections('both');
                    // Load LLM prediction if not already loaded
                    if (!currentLLMPredictionData) {
                        loadLLMPredictionData(currentPriceValue);
                    }
                }
            });
        }
    }

    // Function to show/hide prediction sections
    function showPredictionSections(type) {
        if (!traditionalSection || !llmSection) return;

        switch (type) {
            case 'traditional':
                traditionalSection.style.display = 'block';
                llmSection.style.display = 'none';
                break;
            case 'llm':
                traditionalSection.style.display = 'none';
                llmSection.style.display = 'block';
                break;
            case 'both':
                traditionalSection.style.display = 'block';
                llmSection.style.display = 'block';
                // Add separator between sections
                if (!traditionalSection.classList.contains('mb-4')) {
                    traditionalSection.classList.add('mb-4');
                }
                break;
        }
    }

    // Function to show loading state
    function showLoading() {
        // Show loading state in the new prediction section if it exists
        if (hasNewPredictionSection) {
            console.log('Showing loading state in new prediction section');
            predictionMain.innerHTML = `
                <div class="placeholder-glow">
                    <span class="placeholder col-8"></span>
                </div>
            `;

            predictionConfidence.innerHTML = `
                <div class="placeholder-glow">
                    <span class="placeholder col-12"></span>
                    <span class="placeholder col-12 mt-2"></span>
                    <span class="placeholder col-12 mt-2"></span>
                </div>
            `;

            predictionArticles.innerHTML = `
                <div class="placeholder-glow">
                    <span class="placeholder col-12" style="height: 100px;"></span>
                </div>
            `;
        }
        // Show loading state in the old prediction container only if new section doesn't exist
        else if (predictionContainer) {
            console.log('Showing loading state in old prediction container (fallback)');
            predictionContainer.innerHTML = `
                <div class="card h-100 border-0">
                    <div class="card-body p-2">
                        <div class="d-flex flex-column">
                            <div class="d-flex align-items-baseline justify-content-between">
                                <div class="d-flex align-items-baseline">
                                    <div class="placeholder-glow me-2">
                                        <span class="placeholder col-8" style="height: 2rem;"></span>
                                    </div>
                                    <div class="placeholder-glow">
                                        <span class="placeholder col-8"></span>
                                    </div>
                                </div>
                                <div class="placeholder-glow">
                                    <span class="placeholder col-4"></span>
                                </div>
                            </div>
                            <div class="placeholder-glow mt-1">
                                <span class="placeholder col-6" style="height: 0.75rem;"></span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // Function to load current price first, then prediction
    function loadCurrentPrice() {
        console.log('Loading current price...');
        showLoading();

        // Fetch current price
        fetch('/api/sp500')
            .then(response => {
                console.log('Current price response status:', response.status);
                return response.json();
            })
            .then(priceData => {
                console.log('Received price data:', priceData);

                // Extract the latest price
                if (priceData && priceData.close && priceData.close.length > 0) {
                    const latestPrice = priceData.close[priceData.close.length - 1];
                    console.log('Latest price:', latestPrice);
                    currentPriceValue = latestPrice;

                    // Now load the prediction data
                    loadPredictionData(latestPrice);
                } else {
                    console.error('Invalid price data format:', priceData);
                    // Try to load prediction data anyway
                    loadPredictionData(null);
                }
            })
            .catch(error => {
                console.error('Error fetching current price:', error);
                // If we can't get the current price, still try to load prediction
                loadPredictionData(null);
            });
    }

    // Function to load prediction data
    async function loadPredictionData(currentPrice) {
        console.log('Loading prediction data with current price:', currentPrice);
        try {
            const response = await fetch('/api/prediction');
            console.log('Prediction response status:', response.status);

            const data = await response.json();
            console.log('Received prediction data:', data);

            if (data.error) {
                throw new Error(data.error);
            }

            // Add the current price to the prediction data if it's missing
            if (currentPrice && (!data.current_price || data.current_price === 0)) {
                console.log('Adding current price to prediction data:', currentPrice);
                data.current_price = currentPrice;
            }

            // Ensure probabilities object exists
            if (!data.probabilities) {
                console.log('No probabilities found in prediction data, initializing empty probabilities object');
                data.probabilities = {
                    positive: 0.33,
                    negative: 0.33,
                    neutral: 0.34
                };
            }

            // Store the prediction data for later use
            currentPredictionData = data;

            // Render the prediction
            console.log('Rendering prediction with data:', data);
            renderPrediction(data);
        } catch (error) {
            console.error('Error fetching prediction:', error);
            showErrorMessage(error.message, currentPrice);
        }
    }

    // Function to load LLM prediction data
    async function loadLLMPredictionData(currentPrice, apiName = 'gemini') {
        if (!hasLLMPredictionSection) {
            console.log('LLM prediction section not available');
            return;
        }

        console.log(`Loading LLM prediction data with API: ${apiName}`);

        // Show loading state for LLM section
        showLLMLoading();

        try {
            const response = await fetch(`/api/llm-prediction?api=${apiName}`);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to fetch LLM prediction');
            }

            console.log('LLM prediction data received:', data);
            currentLLMPredictionData = data;
            renderLLMPrediction(data);

        } catch (error) {
            console.error('Error fetching LLM prediction:', error);
            showLLMErrorMessage(error.message);
        }
    }

    // Function to show LLM loading state
    function showLLMLoading() {
        if (!hasLLMPredictionSection) return;

        llmPredictionMain.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm text-info me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span class="text-muted">Analyzing market with AI...</span>
            </div>
        `;

        llmPredictionConfidence.innerHTML = `
            <div class="placeholder-glow">
                <span class="placeholder col-12"></span>
                <span class="placeholder col-12 mt-2"></span>
                <span class="placeholder col-12 mt-2"></span>
            </div>
        `;

        llmKeyEvidence.innerHTML = `
            <div class="placeholder-glow">
                <span class="placeholder col-12" style="height: 60px;"></span>
            </div>
        `;

        llmDominantTheme.innerHTML = `
            <div class="placeholder-glow">
                <span class="placeholder col-10"></span>
            </div>
        `;
    }

    // Function to show LLM error message
    function showLLMErrorMessage(message) {
        if (!hasLLMPredictionSection) return;

        llmPredictionMain.innerHTML = `
            <div class="alert alert-warning">
                <strong>LLM Analysis unavailable</strong>
                <div class="small">Error: ${message}</div>
                <button id="retry-llm-prediction-btn" class="btn btn-outline-warning btn-sm mt-2">
                    <i class="bi bi-arrow-clockwise me-1"></i>Retry
                </button>
            </div>
        `;

        llmPredictionConfidence.innerHTML = `
            <div class="alert alert-warning">
                <div class="small">Unable to load confidence scores</div>
            </div>
        `;

        llmKeyEvidence.innerHTML = `
            <div class="alert alert-warning">
                <div class="small">Unable to load key evidence</div>
            </div>
        `;

        llmDominantTheme.innerHTML = `
            <div class="alert alert-warning">
                <div class="small">Unable to load market theme</div>
            </div>
        `;

        // Add event listener to retry button
        const retryButton = document.getElementById('retry-llm-prediction-btn');
        if (retryButton) {
            retryButton.addEventListener('click', function () {
                const selectedApi = llmApiSelect ? llmApiSelect.value : 'gemini';
                loadLLMPredictionData(currentPriceValue, selectedApi);
            });
        }
    }

    // Function to show error message
    function showErrorMessage(message, currentPrice) {
        // Show error in the new prediction section if it exists
        if (hasNewPredictionSection) {
            console.log('Showing error message in new prediction section');
            predictionMain.innerHTML = `
                <div class="alert alert-danger">
                    <strong>Prediction unavailable</strong>
                    <div class="small">Error: ${message}</div>
                    <button id="retry-prediction-btn-main" class="btn btn-outline-danger btn-sm mt-2">
                        <i class="bi bi-arrow-clockwise me-1"></i>Retry
                    </button>
                </div>
            `;

            predictionConfidence.innerHTML = `
                <div class="alert alert-danger">
                    <div class="small">Unable to load confidence scores</div>
                </div>
            `;

            predictionArticles.innerHTML = `
                <div class="alert alert-danger">
                    <div class="small">Unable to load influential articles</div>
                </div>
            `;

            // Add event listener to retry button
            const retryButton = document.getElementById('retry-prediction-btn-main');
            if (retryButton) {
                retryButton.addEventListener('click', function () {
                    showLoading();
                    loadPredictionData(currentPrice);
                });
            }
        }
        // Show error in the old prediction container only if new section doesn't exist
        else if (predictionContainer) {
            console.log('Showing error message in old prediction container (fallback)');
            predictionContainer.innerHTML = `
                <div class="card h-100 border-0">
                    <div class="card-body p-2">
                        <div class="d-flex flex-column">
                            <div class="d-flex align-items-baseline justify-content-between">
                                <div class="text-danger">Prediction unavailable</div>
                                <button id="retry-prediction-btn" class="btn btn-outline-primary btn-sm" style="font-size: 0.7rem; padding: 0.15rem 0.4rem;">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Retry
                                </button>
                            </div>
                            <div class="small text-danger" style="font-size: 0.75rem;">Error: ${message}</div>
                        </div>
                    </div>
                </div>
            `;

            // Add event listener to retry button
            const retryButton = document.getElementById('retry-prediction-btn');
            if (retryButton) {
                retryButton.addEventListener('click', function () {
                    showLoading();
                    loadPredictionData(currentPrice);
                });
            }
        }
    }

    // Function to render prediction
    function renderPrediction(data) {
        // Get the prediction and confidence
        // Ensure prediction is a string before calling toUpperCase()
        const prediction = data.prediction ? String(data.prediction).toUpperCase() : 'UNKNOWN';
        const confidence = data.confidence ? (data.confidence * 100).toFixed(1) + '%' : 'N/A';

        // Determine color class based on prediction
        let predictionClass = '';
        let predictionIcon = '';
        let predictionBadgeClass = '';

        if (prediction === 'POSITIVE') {
            predictionClass = 'text-success';
            predictionIcon = '<i class="bi bi-graph-up-arrow me-1"></i>';
            predictionBadgeClass = 'bg-success';
        } else if (prediction === 'NEGATIVE') {
            predictionClass = 'text-danger';
            predictionIcon = '<i class="bi bi-graph-down-arrow me-1"></i>';
            predictionBadgeClass = 'bg-danger';
        } else {
            predictionClass = 'text-secondary';
            predictionIcon = '<i class="bi bi-dash me-1"></i>';
            predictionBadgeClass = 'bg-secondary';
        }

        // Get probabilities for each class
        const probabilities = data.probabilities || {};
        console.log('Probabilities from API:', probabilities);

        // Ensure we have actual values for the probabilities, not "N/A"
        const positiveScoreValue = (probabilities.positive !== undefined && probabilities.positive !== null) ? probabilities.positive : 0;
        const negativeScoreValue = (probabilities.negative !== undefined && probabilities.negative !== null) ? probabilities.negative : 0;
        const neutralScoreValue = (probabilities.neutral !== undefined && probabilities.neutral !== null) ? probabilities.neutral : 0;

        console.log('Processed probability values:', {
            positiveScoreValue,
            negativeScoreValue,
            neutralScoreValue
        });

        // Format scores for display
        const positiveScore = (positiveScoreValue * 100).toFixed(1) + '%';
        const negativeScore = (negativeScoreValue * 100).toFixed(1) + '%';
        const neutralScore = (neutralScoreValue * 100).toFixed(1) + '%';

        // Render in the old prediction container only if new section doesn't exist
        if (!hasNewPredictionSection && predictionContainer) {
            console.log('Rendering prediction in old container (fallback)');
            // Create HTML for prediction card
            let html = `
                <div class="card h-100 border-0">
                    <div class="card-body p-2">
                        <div class="d-flex flex-column">
                            <div class="d-flex align-items-baseline justify-content-between">
                                <div class="d-flex align-items-baseline">
                                    <h4 class="me-2 mb-0 ${predictionClass}">${predictionIcon}${prediction}</h4>
                                    <span class="fs-5">${confidence} confidence</span>
                                </div>
                                <button id="run-prediction-btn" class="btn btn-outline-primary btn-sm" style="font-size: 0.7rem; padding: 0.15rem 0.4rem;">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Update
                                </button>
                            </div>

                            <!-- Confidence scores bar chart -->
                            <div class="mt-2">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="small text-success">Positive</span>
                                    <span class="small text-success">${positiveScore}</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-success" role="progressbar"
                                         style="width: ${positiveScoreValue * 100}%;"
                                         aria-valuenow="${positiveScoreValue * 100}"
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-1 mt-2">
                                    <span class="small text-danger">Negative</span>
                                    <span class="small text-danger">${negativeScore}</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-danger" role="progressbar"
                                         style="width: ${negativeScoreValue * 100}%;"
                                         aria-valuenow="${negativeScoreValue * 100}"
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-1 mt-2">
                                    <span class="small text-secondary">Neutral</span>
                                    <span class="small text-secondary">${neutralScore}</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-secondary" role="progressbar"
                                         style="width: ${neutralScoreValue * 100}%;"
                                         aria-valuenow="${neutralScoreValue * 100}"
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add relevant articles button if available
            if (data.relevant_articles && data.relevant_articles.length > 0) {
                html += `
                    <div class="mt-3">
                        <button id="view-articles-btn" class="btn btn-outline-primary btn-sm w-100">
                            <i class="bi bi-newspaper me-1"></i> View Influential Articles (${data.relevant_articles.length})
                        </button>
                    </div>
                `;
            }

            // Add historical accuracy button
            html += `
                <div class="mt-2">
                    <button id="view-history-btn" class="btn btn-outline-secondary btn-sm w-100">
                        <i class="bi bi-graph-up me-1"></i> View Historical Accuracy
                    </button>
                </div>
            `;

            predictionContainer.innerHTML = html;

            // Add event listener to the prediction update button
            const updateButton = document.getElementById('run-prediction-btn');
            if (updateButton) {
                updateButton.addEventListener('click', function () {
                    showLoading();
                    loadPredictionData(currentPriceValue);
                });
            }

            // Add event listener to the view articles button
            const articlesButton = document.getElementById('view-articles-btn');
            if (articlesButton && data.relevant_articles) {
                articlesButton.addEventListener('click', function () {
                    showArticlesModal(data.relevant_articles);
                });
            }

            // Add event listener to the history button
            const historyButton = document.getElementById('view-history-btn');
            if (historyButton) {
                historyButton.addEventListener('click', function () {
                    showHistoryModal();
                });
            }
        }

        // Render in the new prediction section if it exists
        if (hasNewPredictionSection) {
            // Render main prediction
            predictionMain.innerHTML = `
                <div class="d-flex align-items-center">
                    <h3 class="me-2 mb-0 ${predictionClass}">${predictionIcon}${prediction}</h3>
                    <span class="badge ${predictionBadgeClass} fs-6">${confidence}</span>
                </div>
            `;

            // Render confidence scores
            predictionConfidence.innerHTML = `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="small text-success fw-bold">Positive</span>
                        <span class="small text-success fw-bold">${positiveScore}</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-success" role="progressbar"
                             style="width: ${positiveScoreValue * 100}%;"
                             aria-valuenow="${positiveScoreValue * 100}"
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-1 mt-3">
                        <span class="small text-danger fw-bold">Negative</span>
                        <span class="small text-danger fw-bold">${negativeScore}</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-danger" role="progressbar"
                             style="width: ${negativeScoreValue * 100}%;"
                             aria-valuenow="${negativeScoreValue * 100}"
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-1 mt-3">
                        <span class="small text-secondary fw-bold">Neutral</span>
                        <span class="small text-secondary fw-bold">${neutralScore}</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-secondary" role="progressbar"
                             style="width: ${neutralScoreValue * 100}%;"
                             aria-valuenow="${neutralScoreValue * 100}"
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            `;

            // Render influential articles
            if (data.relevant_articles && data.relevant_articles.length > 0) {
                let articlesHtml = `<div class="list-group">`;

                // Show up to 3 articles in the main view
                const articlesToShow = data.relevant_articles.slice(0, 3);

                articlesToShow.forEach(article => {
                    // Get indicator_prediction class
                    let indicator_predictionClass = 'text-secondary';
                    let indicator_predictionIcon = '<i class="bi bi-dash"></i>';
                    let indicator_predictionText = 'Neutral';

                    if (article.indicator_analysis) {
                        const prediction = article.indicator_analysis.label || 'neutral';
                        if (prediction === 'positive') {
                            indicator_predictionClass = 'text-success';
                            indicator_predictionIcon = '<i class="bi bi-graph-up-arrow"></i>';
                            indicator_predictionText = 'Positive';
                        } else if (prediction === 'negative') {
                            indicator_predictionClass = 'text-danger';
                            indicator_predictionIcon = '<i class="bi bi-graph-down-arrow"></i>';
                            indicator_predictionText = 'Negative';
                        }
                    }

                    // Format date
                    const articleDate = article.publish_time ? formatDate(article.publish_time, true) : 'Unknown date';

                    articlesHtml += `
                        <a href="${article.url || '#'}" class="list-group-item list-group-item-action py-2" target="_blank">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">${article.title || 'No title'}</h6>
                                <small class="${indicator_predictionClass}">${indicator_predictionIcon}${indicator_predictionText}</small>
                            </div>
                            <small class="text-muted">
                                ${article.source || 'Unknown source'} - ${articleDate}
                            </small>
                        </a>
                    `;
                });

                articlesHtml += `</div>`;

                if (data.relevant_articles.length > 3) {
                    articlesHtml += `
                        <div class="text-center mt-2">
                            <small class="text-muted">Showing 3 of ${data.relevant_articles.length} articles</small>
                        </div>
                    `;
                }

                predictionArticles.innerHTML = articlesHtml;
            } else {
                predictionArticles.innerHTML = `
                    <div class="alert alert-info">
                        No influential articles available for this prediction.
                    </div>
                `;
            }
        }
    }

    // Function to render LLM prediction
    function renderLLMPrediction(data) {
        if (!hasLLMPredictionSection) return;

        // Get the prediction and confidence
        const prediction = data.prediction ? String(data.prediction).toUpperCase() : 'UNKNOWN';
        const confidence = data.confidence ? (data.confidence * 100).toFixed(1) + '%' : 'N/A';

        // Determine color class based on prediction
        let predictionClass = '';
        let predictionIcon = '';
        let predictionBadgeClass = '';

        if (prediction === 'UP' || prediction === 'POSITIVE') {
            predictionClass = 'text-success';
            predictionIcon = '<i class="bi bi-graph-up-arrow me-1"></i>';
            predictionBadgeClass = 'bg-success';
        } else if (prediction === 'DOWN' || prediction === 'NEGATIVE') {
            predictionClass = 'text-danger';
            predictionIcon = '<i class="bi bi-graph-down-arrow me-1"></i>';
            predictionBadgeClass = 'bg-danger';
        } else {
            predictionClass = 'text-secondary';
            predictionIcon = '<i class="bi bi-dash me-1"></i>';
            predictionBadgeClass = 'bg-secondary';
        }

        // Get probabilities for each class
        const probabilities = data.probabilities || {};
        const positiveScoreValue = probabilities.positive || 0;
        const negativeScoreValue = probabilities.negative || 0;
        const neutralScoreValue = probabilities.neutral || 0;

        // Format scores for display
        const positiveScore = (positiveScoreValue * 100).toFixed(1) + '%';
        const negativeScore = (negativeScoreValue * 100).toFixed(1) + '%';
        const neutralScore = (neutralScoreValue * 100).toFixed(1) + '%';

        // Render main prediction
        llmPredictionMain.innerHTML = `
            <div class="d-flex align-items-center">
                <h3 class="me-2 mb-0 ${predictionClass}">${predictionIcon}${prediction}</h3>
                <span class="badge ${predictionBadgeClass} fs-6">${confidence}</span>
            </div>
        `;

        // Render confidence scores
        llmPredictionConfidence.innerHTML = `
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <span class="small text-success fw-bold">Positive</span>
                    <span class="small text-success fw-bold">${positiveScore}</span>
                </div>
                <div class="progress" style="height: 10px;">
                    <div class="progress-bar bg-success" role="progressbar"
                         style="width: ${positiveScoreValue * 100}%;"
                         aria-valuenow="${positiveScoreValue * 100}"
                         aria-valuemin="0" aria-valuemax="100"></div>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-1 mt-3">
                    <span class="small text-danger fw-bold">Negative</span>
                    <span class="small text-danger fw-bold">${negativeScore}</span>
                </div>
                <div class="progress" style="height: 10px;">
                    <div class="progress-bar bg-danger" role="progressbar"
                         style="width: ${negativeScoreValue * 100}%;"
                         aria-valuenow="${negativeScoreValue * 100}"
                         aria-valuemin="0" aria-valuemax="100"></div>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-1 mt-3">
                    <span class="small text-secondary fw-bold">Neutral</span>
                    <span class="small text-secondary fw-bold">${neutralScore}</span>
                </div>
                <div class="progress" style="height: 10px;">
                    <div class="progress-bar bg-secondary" role="progressbar"
                         style="width: ${neutralScoreValue * 100}%;"
                         aria-valuenow="${neutralScoreValue * 100}"
                         aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
        `;

        // Render key evidence with article references
        const keyEvidence = data.key_evidence || [];
        if (keyEvidence.length > 0) {
            let evidenceHtml = '<ul class="list-unstyled">';
            keyEvidence.slice(0, 4).forEach(evidence => {
                // Handle both old format (string) and new format (object with article references)
                if (typeof evidence === 'object' && evidence.fact) {
                    // New format with article references
                    const articleLink = evidence.url ?
                        `<a href="${evidence.url}" target="_blank" class="text-decoration-none">
                            <i class="bi bi-link-45deg text-primary"></i>
                            ${evidence.article_title || evidence.source || 'Source'}
                        </a>` :
                        (evidence.article_title || evidence.source || '');

                    evidenceHtml += `
                        <li class="mb-3 border-start border-info border-2 ps-3">
                            <div class="d-flex align-items-start">
                                <i class="bi bi-check-circle text-info me-2 mt-1"></i>
                                <div class="flex-grow-1">
                                    <div class="small mb-1">${evidence.fact}</div>
                                    ${articleLink ? `<div class="small text-muted">${articleLink}</div>` : ''}
                                </div>
                            </div>
                        </li>
                    `;
                } else {
                    // Old format - just a string
                    const evidenceText = typeof evidence === 'string' ? evidence : evidence.fact || 'No evidence';
                    evidenceHtml += `
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-info me-2"></i>
                            <span class="small">${evidenceText}</span>
                        </li>
                    `;
                }
            });
            evidenceHtml += '</ul>';
            llmKeyEvidence.innerHTML = evidenceHtml;
        } else {
            llmKeyEvidence.innerHTML = `
                <div class="alert alert-info">
                    <small>No specific evidence points available.</small>
                </div>
            `;
        }

        // Render dominant theme with supporting articles
        const dominantTheme = data.dominant_theme || '';
        if (dominantTheme) {
            let themeHtml = '';

            if (typeof dominantTheme === 'object' && dominantTheme.theme) {
                // New format with supporting articles
                themeHtml = `
                    <div class="alert alert-light border-start border-info border-3">
                        <div class="small text-muted mb-2">${dominantTheme.theme}</div>
                `;

                if (dominantTheme.supporting_articles && dominantTheme.supporting_articles.length > 0) {
                    themeHtml += '<div class="small"><strong>Supporting Articles:</strong><ul class="list-unstyled mt-1 mb-0">';
                    dominantTheme.supporting_articles.slice(0, 3).forEach(article => {
                        const articleLink = article.url ?
                            `<a href="${article.url}" target="_blank" class="text-decoration-none">
                                <i class="bi bi-link-45deg text-primary"></i>
                                ${article.article_title || article.source || 'Source'}
                            </a>` :
                            (article.article_title || article.source || '');

                        themeHtml += `
                            <li class="ms-2 mb-1">
                                <i class="bi bi-dot"></i>
                                ${articleLink}
                                ${article.relevance ? `<div class="text-muted ms-3 small">${article.relevance}</div>` : ''}
                            </li>
                        `;
                    });
                    themeHtml += '</ul></div>';
                }

                themeHtml += '</div>';
            } else {
                // Old format - just a string
                const themeText = typeof dominantTheme === 'string' ? dominantTheme : dominantTheme.theme || 'No theme';
                themeHtml = `
                    <div class="alert alert-light border-start border-info border-3">
                        <small class="text-muted">${themeText}</small>
                    </div>
                `;
            }

            llmDominantTheme.innerHTML = themeHtml;
        } else {
            llmDominantTheme.innerHTML = `
                <div class="alert alert-info">
                    <small>No dominant theme identified.</small>
                </div>
            `;
        }

        // Update API badge
        const apiUsed = data.api_used || data.metadata?.api || 'unknown';
        if (llmApiBadge) {
            llmApiBadge.textContent = apiUsed.charAt(0).toUpperCase() + apiUsed.slice(1);
        }
    }

    // Function to show articles modal
    function showArticlesModal(articles) {
        // Create modal ID
        const modalId = 'relevantArticlesModal';

        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${modalId}Label">Articles Influencing Prediction</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="list-group">
                                ${articles.map(article => {
            // Get indicator_prediction class
            let indicator_predictionClass = 'text-secondary';
            let indicator_predictionIcon = '<i class="bi bi-dash"></i>';
            let indicator_predictionText = 'Neutral';

            if (article.indicator_analysis) {
                const indicator_prediction = article.indicator_analysis.label || 'neutral';
                if (indicator_prediction === 'positive') {
                    indicator_predictionClass = 'text-success';
                    indicator_predictionIcon = '<i class="bi bi-graph-up-arrow"></i>';
                    indicator_predictionText = 'Positive';
                } else if (indicator_prediction === 'negative') {
                    indicator_predictionClass = 'text-danger';
                    indicator_predictionIcon = '<i class="bi bi-graph-down-arrow"></i>';
                    indicator_predictionText = 'Negative';
                }
            }

            // Format date
            const articleDate = article.date ? formatDate(article.date) : 'Unknown date';

            return `
                                        <a href="${article.url || '#'}" class="list-group-item list-group-item-action" target="_blank">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h5 class="mb-1">${article.title || 'No title'}</h5>
                                                <small class="${indicator_predictionClass}">${indicator_predictionIcon} ${indicator_predictionText}</small>
                                            </div>
                                            <p class="mb-1">${article.summary || 'No summary available'}</p>
                                            <small class="text-muted">
                                                ${article.source || 'Unknown source'} - ${articleDate}
                                            </small>
                                        </a>
                                    `;
        }).join('')}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page if it doesn't exist
        if (!document.getElementById(modalId)) {
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();
    }

    // Function to show history modal
    function showHistoryModal() {
        // Create modal IDs
        const historyModalId = 'predictionHistoryModal';
        const historyChartId = 'predictionHistoryChart';

        // Create modal HTML
        const historyModalHtml = `
            <div class="modal fade" id="${historyModalId}" tabindex="-1" aria-labelledby="${historyModalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${historyModalId}Label">Prediction History & Accuracy</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div id="${historyChartId}" style="height: 300px;"></div>
                            <div id="history-table-container" class="mt-3">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p>Loading prediction history...</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page if it doesn't exist
        if (!document.getElementById(historyModalId)) {
            document.body.insertAdjacentHTML('beforeend', historyModalHtml);
        }

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById(historyModalId));
        modal.show();

        // Load prediction history
        loadPredictionHistory();
    }

    // Function to load prediction history
    async function loadPredictionHistory() {
        const historyChartContainer = document.getElementById('predictionHistoryChart');
        const historyTableContainer = document.getElementById('history-table-container');

        if (!historyChartContainer || !historyTableContainer) return;

        try {
            // Fetch prediction history
            const response = await fetch('/api/prediction-history?limit=20');
            const historyData = await response.json();

            if (!historyData || historyData.length === 0) {
                historyTableContainer.innerHTML = '<div class="alert alert-info">No prediction history available yet.</div>';
                return;
            }

            // Prepare data for chart
            const dates = [];
            const positiveScores = [];
            const negativeScores = [];
            const neutralScores = [];

            historyData.forEach(prediction => {
                if (prediction.prediction_date) {
                    // Format date for display
                    const date = new Date(prediction.prediction_date + 'T00:00:00');
                    const formattedDate = date.toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric'
                    });

                    dates.push(formattedDate);

                    // Get probabilities
                    const probabilities = prediction.probabilities || {};
                    positiveScores.push(probabilities.positive ? probabilities.positive * 100 : 0);
                    negativeScores.push(probabilities.negative ? probabilities.negative * 100 : 0);
                    neutralScores.push(probabilities.neutral ? probabilities.neutral * 100 : 0);
                }
            });

            // Reverse arrays to show oldest to newest
            dates.reverse();
            positiveScores.reverse();
            negativeScores.reverse();
            neutralScores.reverse();

            // Create chart
            Plotly.newPlot(historyChartContainer, [
                {
                    x: dates,
                    y: positiveScores,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Positive',
                    line: { color: 'green' }
                },
                {
                    x: dates,
                    y: negativeScores,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Negative',
                    line: { color: 'red' }
                },
                {
                    x: dates,
                    y: neutralScores,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Neutral',
                    line: { color: 'gray' }
                }
            ], {
                title: 'Prediction Confidence History',
                xaxis: { title: 'Date' },
                yaxis: { title: 'Confidence (%)', range: [0, 100] },
                margin: { l: 50, r: 50, b: 50, t: 50, pad: 4 },
                legend: { orientation: 'h', y: -0.2 }
            }, {
                responsive: true
            });

            // Create history table
            let tableHtml = `
                <h5 class="mt-4">Recent Predictions</h5>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Prediction</th>
                                <th>Confidence</th>
                                <th>Positive</th>
                                <th>Negative</th>
                                <th>Neutral</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            // Add rows for each prediction
            historyData.forEach(prediction => {
                const predictionDate = formatDate(prediction.prediction_date);
                // Ensure prediction is a string before calling toUpperCase()
                const predictionText = prediction.prediction ? String(prediction.prediction).toUpperCase() : 'UNKNOWN';
                const confidence = prediction.confidence ? (prediction.confidence * 100).toFixed(1) + '%' : 'N/A';

                // Get probabilities
                const probabilities = prediction.probabilities || {};
                const positiveScore = probabilities.positive ? (probabilities.positive * 100).toFixed(1) + '%' : 'N/A';
                const negativeScore = probabilities.negative ? (probabilities.negative * 100).toFixed(1) + '%' : 'N/A';
                const neutralScore = probabilities.neutral ? (probabilities.neutral * 100).toFixed(1) + '%' : 'N/A';

                // Determine class for prediction
                let predictionClass = '';
                if (predictionText === 'POSITIVE') {
                    predictionClass = 'text-success';
                } else if (predictionText === 'NEGATIVE') {
                    predictionClass = 'text-danger';
                }

                tableHtml += `
                    <tr>
                        <td>${predictionDate}</td>
                        <td class="${predictionClass}">${predictionText}</td>
                        <td>${confidence}</td>
                        <td>${positiveScore}</td>
                        <td>${negativeScore}</td>
                        <td>${neutralScore}</td>
                    </tr>
                `;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            historyTableContainer.innerHTML = tableHtml;
        } catch (error) {
            console.error('Error loading prediction history:', error);
            historyTableContainer.innerHTML = `
                <div class="alert alert-danger">
                    Error loading prediction history: ${error.message}
                </div>
            `;
        }
    }

    // Helper function to format date
    function formatDate(dateStr, compact = false) {
        try {
            // Parse the date string and handle timezone issues
            let dateToUse = dateStr;
            if (dateStr.indexOf('T') === -1) {
                // If there's no time part, add it to ensure consistent parsing
                dateToUse = `${dateStr}T12:00:00`;
            }

            const date = new Date(dateToUse);

            // Format the date based on the compact parameter
            if (compact) {
                // Compact format: "Jan 1, 2023"
                return date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                });
            } else {
                // Full format: "Monday, January 1, 2023"
                return date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            }
        } catch (e) {
            console.error(`Error formatting date: ${e}`);
            return dateStr;
        }
    }

    // Function to show LLM detailed outlook modal
    function showLLMOutlookModal(data) {
        const modalId = 'llmOutlookModal';
        const detailedOutlook = data.detailed_outlook || {};

        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${modalId}Label">
                                <i class="bi bi-robot text-info me-2"></i>LLM Detailed Market Outlook
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            ${renderOutlookSection('Short-term Outlook', detailedOutlook.short_term, 'primary')}
                            ${renderOutlookSection('Medium-term Outlook', detailedOutlook.medium_term, 'warning')}
                            ${renderOutlookSection('Long-term Outlook', detailedOutlook.long_term, 'info')}

                            ${data.dominant_theme ? `
                                <div class="mt-4">
                                    <h6 class="text-muted">Dominant Market Theme</h6>
                                    <div class="alert alert-light border-start border-info border-3">
                                        ${data.dominant_theme}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page if it doesn't exist
        if (!document.getElementById(modalId)) {
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();
    }

    // Function to render outlook section
    function renderOutlookSection(title, outlook, colorClass) {
        if (!outlook || Object.keys(outlook).length === 0) {
            return `
                <div class="mb-4">
                    <h6 class="text-${colorClass}">${title}</h6>
                    <div class="alert alert-light">
                        <small class="text-muted">No ${title.toLowerCase()} analysis available.</small>
                    </div>
                </div>
            `;
        }

        const direction = outlook.direction || 'FLAT';
        const expectedMove = outlook.expected_move_pct || 'Unknown';
        const confidence = outlook.confidence || 'LOW';
        const keyEvidence = outlook.key_evidence || [];

        // Direction icon and color
        let directionIcon = '<i class="bi bi-dash"></i>';
        let directionColor = 'text-secondary';
        if (direction === 'UP') {
            directionIcon = '<i class="bi bi-graph-up-arrow"></i>';
            directionColor = 'text-success';
        } else if (direction === 'DOWN') {
            directionIcon = '<i class="bi bi-graph-down-arrow"></i>';
            directionColor = 'text-danger';
        }

        // Confidence badge color
        let confidenceBadge = 'bg-secondary';
        if (confidence === 'HIGH') {
            confidenceBadge = 'bg-success';
        } else if (confidence === 'MEDIUM') {
            confidenceBadge = 'bg-warning';
        }

        return `
            <div class="mb-4">
                <h6 class="text-${colorClass}">${title}</h6>
                <div class="card border-${colorClass}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>Direction:</strong>
                                    <span class="${directionColor} ms-2">${directionIcon} ${direction}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Expected Move:</strong>
                                    <span class="ms-2">${expectedMove}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Confidence:</strong>
                                    <span class="badge ${confidenceBadge} ms-2">${confidence}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                ${keyEvidence.length > 0 ? `
                                    <strong>Key Evidence:</strong>
                                    <ul class="small mt-1 mb-0">
                                        ${keyEvidence.map(evidence => {
            if (typeof evidence === 'object' && evidence.fact) {
                // New format with article references
                const articleLink = evidence.url ?
                    `<br><a href="${evidence.url}" target="_blank" class="text-decoration-none text-primary">
                                                        <i class="bi bi-link-45deg"></i> ${evidence.article_title || evidence.source || 'Source'}
                                                    </a>` : '';
                return `<li>${evidence.fact}${articleLink}</li>`;
            } else {
                // Old format - just a string
                return `<li>${evidence}</li>`;
            }
        }).join('')}
                                    </ul>
                                ` : '<small class="text-muted">No specific evidence available.</small>'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Function to show critical levels modal
    function showCriticalLevelsModal(data) {
        const modalId = 'criticalLevelsModal';
        const criticalLevels = data.critical_levels || {};
        const currentPrice = data.current_price || data.input_metadata?.current_price || 0;

        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${modalId}Label">
                                <i class="bi bi-bar-chart text-secondary me-2"></i>Critical Price Levels
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            ${Object.keys(criticalLevels).length > 0 ? `
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card border-success mb-3">
                                            <div class="card-header bg-success text-white">
                                                <i class="bi bi-shield-check me-2"></i>Support Level
                                            </div>
                                            <div class="card-body">
                                                <h4 class="text-success">
                                                    ${criticalLevels.support ? '$' + criticalLevels.support.toFixed(2) : 'Not specified'}
                                                </h4>
                                                <small class="text-muted">
                                                    Price level where buying interest is expected to emerge
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-danger mb-3">
                                            <div class="card-header bg-danger text-white">
                                                <i class="bi bi-shield-x me-2"></i>Resistance Level
                                            </div>
                                            <div class="card-body">
                                                <h4 class="text-danger">
                                                    ${criticalLevels.resistance ? '$' + criticalLevels.resistance.toFixed(2) : 'Not specified'}
                                                </h4>
                                                <small class="text-muted">
                                                    Price level where selling pressure is expected to increase
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                ${currentPrice > 0 ? `
                                    <div class="card border-primary mb-3">
                                        <div class="card-header bg-primary text-white">
                                            <i class="bi bi-graph-up me-2"></i>Current Position
                                        </div>
                                        <div class="card-body">
                                            <h4 class="text-primary">$${currentPrice.toFixed(2)}</h4>
                                            <div class="row">
                                                ${criticalLevels.support ? `
                                                    <div class="col-6">
                                                        <small class="text-muted">Distance to Support:</small><br>
                                                        <span class="${currentPrice > criticalLevels.support ? 'text-success' : 'text-danger'}">
                                                            ${((currentPrice - criticalLevels.support) / criticalLevels.support * 100).toFixed(1)}%
                                                        </span>
                                                    </div>
                                                ` : ''}
                                                ${criticalLevels.resistance ? `
                                                    <div class="col-6">
                                                        <small class="text-muted">Distance to Resistance:</small><br>
                                                        <span class="${currentPrice < criticalLevels.resistance ? 'text-success' : 'text-danger'}">
                                                            ${((criticalLevels.resistance - currentPrice) / currentPrice * 100).toFixed(1)}%
                                                        </span>
                                                    </div>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>
                                ` : ''}

                                ${criticalLevels.trigger ? `
                                    <div class="alert alert-info">
                                        <strong><i class="bi bi-lightning me-2"></i>Breakout/Breakdown Trigger:</strong><br>
                                        ${criticalLevels.trigger}
                                    </div>
                                ` : ''}
                            ` : `
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    No critical price levels identified in this analysis.
                                </div>
                            `}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page if it doesn't exist
        if (!document.getElementById(modalId)) {
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();
    }

    // Register the function with the global NewsMonitor object
    window.NewsMonitor.loadPrediction = function (currentPrice) {
        console.log('loadPrediction called with price:', currentPrice);
        currentPriceValue = currentPrice;

        // Load prediction data
        console.log('Loading prediction data');
        loadPredictionData(currentPrice);
    };
}
